# 大麦网高并发实战项目

## 项目介绍

本项目是极度真实还原大麦网的高并发实战项目，包含完整的微服务架构、高并发处理、分布式系统设计等核心技术。

## 核心功能模块

### 1. 分布式ID生成器

**介绍**
- 基于开源百度分布式id源码级别改造来适配spring-boot项目结构，使用时可直接引入该模块即可
- 在原有数据库的基础上新增了redis的方式，如果配置了spring-boot的redis参数配置，即可自动为redis生成workId

### 2. 接口防刷系统

**系统架构**
本项目实现了一套多层次、高性能的接口防刷系统，通过以下三个维度提供全面防护：

#### 2.1 网关层防刷 (Gateway Layer)
- **线程级限流**: 基于Semaphore的高效并发控制
- **路径匹配检查**: 支持Ant风格路径模式匹配
- **Redis+Lua原子性防刷**: 通过Lua脚本确保操作原子性

#### 2.2 验证码防刷 (Captcha Layer)
- **GET接口限制**: 1分钟120次，失败5次锁定5分钟
- **CHECK/VERIFY接口限制**: 1分钟600次
- **智能锁定机制**: 基于ClientUid的精准控制

#### 2.3 业务层防重复 (Business Layer)
- **分布式锁机制**: 基于Redisson的Fair锁实现
- **幂等性控制**: 防重复提交和重复执行
- **注解式防护**: @RepeatExecuteLimit注解简化使用

**技术特点**
- ✅ **高性能**: Lua脚本原子性操作，避免并发竞争
- ✅ **高可用**: 多层防护，单点故障不影响整体
- ✅ **易扩展**: 插件化设计，支持自定义防刷策略
- ✅ **可监控**: 完整的监控指标和告警机制

**可视化流程图**
项目根目录提供了 `接口防刷系统流程图.canvas` 文件，使用Obsidian Advanced Canvas格式展示完整的防刷流程，包括：
- 主流程：从左到右的核心防刷链路
- 子流程：从上到下的详细实现机制
- 数据流：请求数据的处理和转换过程
- 监控配置：系统监控和动态配置管理

**详细文档**
查看 `接口防刷系统详细技术文档.md` 获取完整的技术实现细节、配置说明和使用示例。

## 项目结构

```
damai_pro-master/
├── 接口防刷系统流程图.canvas          # Obsidian Canvas可视化流程图
├── 接口防刷系统详细技术文档.md         # 详细技术文档
├── 防刷系统使用示例.md                # 使用示例和配置说明
├── Gateway网关层Redis+Lua接口防刷详细分析.md  # 网关层实现分析
├── damai-server/                      # 服务端代码
│   ├── damai-gateway-service/         # 网关服务
│   ├── damai-user-service/           # 用户服务
│   └── ...
├── damai-captcha-manage-framework/    # 验证码防刷框架
├── damai-redisson-framework/          # 分布式锁框架
└── ...
```

## 快速开始

### 环境要求
- JDK 17+
- Redis 6.0+
- MySQL 8.0+
- Maven 3.6+

### 启动步骤
1. 配置Redis连接参数
2. 配置数据库连接
3. 启动网关服务
4. 启动业务服务

### 防刷配置示例

```yaml
# 网关层配置
rate:
  switch: true      # 限流开关
  permits: 200      # 并发许可数

api:
  limit:
    paths:
      - /**/order/**
      - /**/payment/**
```

```java
// 业务层防重复注解使用
@PostMapping("/create")
@RepeatExecuteLimit(
    name = "order_create",
    keys = {"#dto.userId", "#dto.productId"},
    durationTime = 300,
    message = "订单创建过于频繁，请稍后再试"
)
public ApiResponse<String> createOrder(@RequestBody OrderCreateDto dto) {
    return ApiResponse.success(orderService.createOrder(dto));
}
```

## 技术栈

- **微服务框架**: Spring Cloud Gateway, Spring Boot
- **分布式锁**: Redisson
- **缓存**: Redis
- **消息队列**: Kafka
- **数据库**: MySQL
- **监控**: 自定义监控指标
- **脚本**: Lua (Redis)

## 联系方式

添加 阿星不是程序员 微信，添加时备注 大麦 来获取项目的完整资料

