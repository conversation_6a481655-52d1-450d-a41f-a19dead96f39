# 项目中幂等控制的使用场景分析

## 一、幂等控制常量定义

```java
public class RepeatExecuteLimitConstants {
    public static final String CONSUMER_API_DATA_MESSAGE = "consumer_api_data_message";
    public static final String CREATE_PROGRAM_ORDER = "create_program_order";
    public final static String CANCEL_PROGRAM_ORDER = "cancel_program_order";
    public static final String CREATE_PROGRAM_ORDER_MQ = "create_program_order_mq";
    public static final String PROGRAM_CACHE_REVERSE_MQ = "program_cache_reverse_mq";
    public final static String PAY_OR_CANCEL_PROGRAM_ORDER = "pay_or_cancel_program_order";
    public final static String REDUCE_REMAIN_NUMBER = "reduce_remain_number";
}
```

## 二、具体使用场景分析

### 2.1 购票订单创建场景（核心业务）

**使用位置**：所有版本的购票策略（V1-V4）

```java
@RepeatExecuteLimit(
    name = RepeatExecuteLimitConstants.CREATE_PROGRAM_ORDER,
    keys = {"#programOrderCreateDto.userId","#programOrderCreateDto.programId"})
@ServiceLock(name = PROGRAM_ORDER_CREATE_V1,keys = {"#programOrderCreateDto.programId"})
@Override
public String createOrder(final ProgramOrderCreateDto programOrderCreateDto) {
    // 防止同一用户对同一节目重复下单
}
```

**业务目的**：
- **防重复下单**：防止用户快速点击导致重复创建订单
- **锁定范围**：`userId + programId`，确保同一用户对同一节目的幂等性
- **应用版本**：V1、V2、V3、V4所有购票策略版本

### 2.2 订单取消场景

```java
@RepeatExecuteLimit(name = CANCEL_PROGRAM_ORDER,keys = {"#orderCancelDto.orderNumber"})
@ServiceLock(name = ORDER_CANCEL_LOCK,keys = {"#orderCancelDto.orderNumber"})
@Transactional(rollbackFor = Exception.class)
public boolean cancel(OrderCancelDto orderCancelDto){
    // 防止重复取消同一订单
}
```

**业务目的**：
- **防重复取消**：防止用户多次点击取消按钮
- **锁定范围**：`orderNumber`，确保同一订单的取消操作幂等性
- **数据一致性**：避免订单状态被重复修改

### 2.3 消息队列订单创建场景

```java
@RepeatExecuteLimit(name = CREATE_PROGRAM_ORDER_MQ,keys = {"#orderCreateMq.orderNumber"})
@Transactional(rollbackFor = Exception.class)
public String createMq(OrderCreateMq orderCreateMq){
    // 防止消息队列重复消费导致重复创建订单
}
```

**业务目的**：
- **防重复消费**：防止Kafka消息重复消费导致重复创建订单
- **锁定范围**：`orderNumber`，确保同一订单号的消息幂等性
- **消息可靠性**：保证消息处理的一致性

### 2.4 API数据记录场景

```java
@RepeatExecuteLimit(name = RepeatExecuteLimitConstants.CONSUMER_API_DATA_MESSAGE,keys = {"#apiData.id"})
public void saveApiData(ApiData apiData){
    // 防止API调用记录重复保存
}
```

**业务目的**：
- **防重复记录**：防止API调用记录被重复保存到数据库
- **锁定范围**：`apiData.id`，确保同一API记录的幂等性
- **数据准确性**：保证API统计数据的准确性

## 三、幂等控制的技术实现

### 3.1 注解参数配置

```java
@RepeatExecuteLimit(
    name = "业务名称",           // 业务场景标识
    keys = {"#参数.字段"},       // SpEL表达式，构建唯一键
    durationTime = 300,        // 幂等时间窗口（秒）
    message = "自定义提示信息"    // 重复操作时的提示
)
```

### 3.2 锁Key构建规则

```
最终Redis Key格式：
repeat_flag + name + keys组合

示例：
- 购票：repeat_flag_create_program_order_userId_programId
- 取消：repeat_flag_cancel_program_order_orderNumber
- 消息：repeat_flag_create_program_order_mq_orderNumber
```

### 3.3 双重保护机制

```java
// 1. 本地锁（JVM级别）- 快速失败
ReentrantLock localLock = localLockCache.getLock(lockName,true);
boolean localLockResult = localLock.tryLock();

// 2. 分布式锁（集群级别）- 跨JVM保护
ServiceLocker lock = serviceLockFactory.getLock(LockType.Fair);
boolean result = lock.tryLock(lockName, TimeUnit.SECONDS, 0);

// 3. Redis标记（持久化）- 时间窗口控制
if (durationTime > 0) {
    redissonDataHandle.set(repeatFlagName,SUCCESS_FLAG,durationTime,TimeUnit.SECONDS);
}
```

## 四、不同场景的幂等策略

| 业务场景 | 幂等Key构成 | 时间窗口 | 保护目标 | 风险控制 |
|---------|------------|---------|----------|----------|
| **购票下单** | userId + programId | 方法执行期间 | 防重复下单 | 用户体验 + 库存准确性 |
| **订单取消** | orderNumber | 方法执行期间 | 防重复取消 | 订单状态一致性 |
| **消息消费** | orderNumber | 方法执行期间 | 防重复消费 | 数据一致性 |
| **API记录** | apiData.id | 方法执行期间 | 防重复记录 | 统计数据准确性 |

## 五、幂等控制的业务价值

### 5.1 用户体验保护
- **快速响应**：本地锁实现纳秒级重复请求拦截
- **友好提示**：自定义错误信息，提升用户体验
- **防误操作**：避免用户快速点击导致的重复操作

### 5.2 数据一致性保障
- **库存准确性**：防止超卖和重复扣减
- **订单状态**：确保订单状态变更的原子性
- **统计数据**：保证业务数据的准确性

### 5.3 系统稳定性
- **减少无效请求**：快速过滤重复请求，降低系统负载
- **防止雪崩**：避免重复操作导致的系统压力
- **资源保护**：减少数据库和缓存的无效访问

## 六、幂等控制的设计亮点

### 6.1 分层防护
```
第一层：本地锁（纳秒级响应）
第二层：分布式锁（跨JVM保护）  
第三层：Redis标记（时间窗口控制）
```

### 6.2 灵活配置
- **业务隔离**：不同业务使用不同的name标识
- **粒度控制**：通过keys参数精确控制幂等范围
- **时间控制**：可配置幂等时间窗口

### 6.3 高性能设计
- **本地优先**：优先使用本地锁，减少网络开销
- **非阻塞**：使用tryLock()避免线程阻塞
- **缓存优化**：Caffeine本地缓存提升性能

## 七、总结

这套幂等控制机制覆盖了大麦网项目的核心业务场景，通过多层防护和灵活配置，既保证了数据一致性，又提升了用户体验和系统性能。主要应用场景包括：

1. **购票下单**：防止用户重复下单，保护库存准确性
2. **订单取消**：防止重复取消操作，确保订单状态一致性
3. **消息消费**：防止消息重复消费，保证数据处理的幂等性
4. **API记录**：防止重复记录，确保统计数据准确性

通过本地锁、分布式锁、Redis标记的三层防护机制，实现了高性能、高可靠的幂等控制方案。
