<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.example</groupId>
        <artifactId>damai_pro</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>damai-spring-cloud-framework</artifactId>
    <name>spring-cloud-framework</name>
    <description>spring-cloud组件</description>
    <packaging>pom</packaging>

    <modules>
        <module>damai-service-component</module>
        <module>damai-service-common</module>
        <module>damai-service-initialize</module>
        <module>damai-service-gray-transition-framework</module>
    </modules>

</project>
