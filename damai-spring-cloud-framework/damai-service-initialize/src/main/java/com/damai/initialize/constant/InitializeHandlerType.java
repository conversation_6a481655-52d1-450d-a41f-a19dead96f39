package com.damai.initialize.constant;

/**
 * @program: 极度真实还原大麦网高并发实战项目。 添加 阿星不是程序员 微信，添加时备注 大麦 来获取项目的完整资料 
 * @description: 初始化执行 不同策略类型
 * @author: 阿星不是程序员
 **/
public class InitializeHandlerType {
    
    public static final String APPLICATION_EVENT_LISTENER = "application_event_listener";
    
    public static final String APPLICATION_POST_CONSTRUCT = "application_post_construct";
    
    public static final String APPLICATION_INITIALIZING_BEAN = "application_initializing_bean";
    
    public static final String APPLICATION_COMMAND_LINE_RUNNER = "application_command_line_runner";
}
