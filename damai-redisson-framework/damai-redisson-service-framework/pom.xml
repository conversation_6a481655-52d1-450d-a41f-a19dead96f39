<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.example</groupId>
        <artifactId>damai-redisson-framework</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>damai-redisson-service-framework</artifactId>
    <name>damai-redisson-service-framework</name>
    <description>分布式服务工具</description>
    <packaging>pom</packaging>

    <modules>
        <module>damai-redisson-common-framework</module>
		<module>damai-service-lock-framework</module>
        <module>damai-repeat-execute-limit-framework</module>
        <module>damai-bloom-filter-framework</module>
    </modules>

</project>
