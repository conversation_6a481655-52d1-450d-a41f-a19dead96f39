#服务端口
server:
  port: 8081
# 应用名称
spring:
  profiles:
    active: local
  application:
    name: ${prefix.distinction.name:damai}-order-service
  main:
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
    servlet:
      load-on-startup: 1
    jackson:
      time-zone: GMT+8
      date-format: yyyy-MM-dd HH:mm:ss
      generator:
        WRITE_NUMBERS_AS_STRINGS: true
  datasource:
    driver-class-name: org.apache.shardingsphere.driver.ShardingSphereDriver
    url: jdbc:shardingsphere:classpath:shardingsphere-order-${spring.profiles.active}.yaml
  data:
    redis:
      database: 0
      host: 127.0.0.1
      port: 6379
      timeout: 3000
  cloud:
    nacos:
      discovery:
        server-addr: 127.0.0.1:8848
        username: nacos
        password: nacos
  kafka:
    bootstrap-servers: 127.0.0.1:9092
    consumer:
      #默认的消费组ID
      group-id: create_order_data
      #是否自动提交offset
      enable-auto-commit: true
      #提交offset延时
      auto-commit-interval: 200
      # 当kafka中没有初始offset或offset超出范围时将自动重置offset
      # earliest:重置为分区中最小的offset;
      # latest:重置为分区中最新的offset(消费分区中新产生的数据);
      # none:只要有一个分区不存在已提交的offset,就抛出异常;
      auto-offset-reset: latest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      topic: create_order
      
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  global-config:
    db-config:
      logic-delete-field: status
      logic-delete-value: 0
      logic-not-delete-value: 1
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    local-cache-scope: statement
feign:
  sentinel:
    enabled: false
  hystrix:
    enabled: true
  httpclient:
    enabled: false
  okhttp:
    enabled: true
  compression:
    request:
      enabled: true
      mime-types: text/xml,application/xml,application/json
      min-request-size: 512
    response:
      enabled: true
management:
  endpoints:
    web:
      exposure:
        include: '*'
    health:
      show-details: always
  security:
    enabled: false
  health:
    elasticsearch:
      enabled: false
jasypt:
  encryptor:
    password: bgtjkjl!%^sdc
    algorithm: PBEWithMD5AndDES
delay:
  queue:
    corePoolSize: 12
    maximumPoolSize: 24
    isolationRegionCount: 4
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      #生成文档所需的扫包路径，一般为启动类目录
      packages-to-scan: com.damai.controller
#knife4j配置
knife4j:
  #是否启用增强设置
  enable: true
  #开启生产环境屏蔽
  production: false
  #是否启用登录认证
  basic:
    enable: false
  setting:
    language: zh_cn
    enable-version: true
    enable-swagger-models: true
    
seata:
  enabled: true                                 # 启动事务中间件
  application-id: order-service
  data-source-proxy-mode: AT                    # 事务模式，默认AT
  enable-auto-data-source-proxy: true           # 开启数据源自动代理
  transport:
    enable-client-batch-send-request: true      # 客户端事务消息请求是否批量合并发送，默认true，false单条发送
    enable-rm-client-batch-send-request: true   # RM(资源管理器)客户端会将多个分支事务的请求批量发送给 TC（Transaction Coordinator，事务协调器），提高事务处理效率
  tx-service-group: order-service # 事务组名称(可自定义)
  service:
    disable-global-transaction: false
    enable-degrade: false
    # 事务组映射到组列表处理
    vgroup-mapping:
      # 事务组名称作为映射k
      order-service: order-service-map
    grouplist:
      # 映射值作为组k，交给seata服务处理
      order-service-map: 127.0.0.1:8091    