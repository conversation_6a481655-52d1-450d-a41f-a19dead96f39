<template>
  <div :class="{ 'hidden': hidden }" class="pagination-container">
    <el-pagination
        :background="background"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :layout="layout"
        :page-sizes="pageSizes"
        :pager-count="pagerCount"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import {scrollTo} from '@/utils/scroll-to'
import {computed} from 'vue'

const props = defineProps({
  total: {
    required: true,
    type: Number
  },
  page: {
    type: Number,
    default: 1
  },
  limit: {
    type: Number,
    default: 20
  },
  pageSizes: {
    type: Array,
    default() {
      return [10, 20, 30, 50]
    }
  },
  // 移动端页码按钮的数量端默认值5
  pagerCount: {
    type: Number,
    default: document.body.clientWidth < 992 ? 5 : 7
  },
  layout: {
    type: String,
    default: 'total, sizes, prev, pager, next, jumper'
  },
  background: {
    type: Boolean,
    default: true
  },
  autoScroll: {
    type: Boolean,
    default: true
  },
  hidden: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits();
const currentPage = computed({
  get() {
    return props.page
  },
  set(val) {
    emit('update:page', val)
  }
})
const pageSize = computed({
  get() {
    return props.limit
  },
  set(val) {
    emit('update:limit', val)
  }
})

function handleSizeChange(val) {
  if (currentPage.value * val > props.total) {
    currentPage.value = 1
  }
  emit('pagination', {page: currentPage.value, limit: val})
  if (props.autoScroll) {
    scrollTo(0, 800)
  }
}

function handleCurrentChange(val) {
  emit('pagination', {page: val, limit: pageSize.value})
  if (props.autoScroll) {
    scrollTo(0, 800)
  }
}

</script>

<style scoped>
:deep(.pagination-container) {
  background: #fff;
  padding: 0px 16px;
}

:deep(.pagination-container.hidden) {
  display: none;
}

:deep(.el-pagination .is-first)  {
  display: none !important;
}

:deep(.el-pagination .el-pagination__sizes) {
  display: none !important;
}
:deep(.el-icon svg){
  display: block !important;
}
:deep(.el-pagination.is-background .btn-next.is-active),
:deep( .el-pagination.is-background .btn-prev.is-active),
:deep(.el-pagination.is-background .el-pager li.is-active){
  background: rgba(255, 55, 29, 0.85)!important;
}
:deep(.el-pagination .is-last){
  display: none !important;
}
</style>
