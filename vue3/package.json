{"name": "barley", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@vueup/vue-quill": "1.1.0", "@vueuse/core": "9.5.0", "axios": "0.27.2", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "echarts": "5.4.0", "element-plus": "2.2.27", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-cookie": "^3.0.1", "jsencrypt": "^3.3.1", "jsrsasign": "^11.1.0", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "2.0.22", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-router": "4.1.4"}, "devDependencies": {"@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "sass": "1.56.1", "unplugin-auto-import": "^0.17.5", "unplugin-vue-components": "^0.26.0", "vite": "3.2.3", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0"}}